import json
import base64
import numpy as np
from pydub import AudioSegment
from pydub.utils import which
import io

# Configure ffmpeg path (choose one method)
# Method 1: Set ffmpeg path directly
AudioSegment.converter = "/Users/<USER>/ffmpeg"  # Replace with your ffmpeg path
AudioSegment.ffmpeg = "/Users/<USER>/ffmpeg"
# AudioSegment.ffprobe = "/usr/local/bin/ffprobe"

# Method 2: Set via environment (uncomment if needed)
# import os
# os.environ["PATH"] += os.pathsep + "/usr/local/bin"


def convert_audio_json_to_mp3(json_data, output_file="output.mp3"):
    """
    Convert audio JSON object to MP3 file

    Args:
        json_data: Dictionary containing audio configuration and base64 content
        output_file: Output MP3 filename
    """

    # Extract audio configuration
    audio_config = json_data.get("audioConfig", {})
    audio_content = json_data.get("audioContent", "")

    # Get audio parameters
    sample_rate = audio_config.get("sampleRateHertz", 24000)
    encoding = audio_config.get("audioEncoding", "LINEAR16")

    # Decode base64 audio content
    try:
        audio_bytes = base64.b64decode(audio_content)
    except Exception as e:
        raise ValueError(f"Failed to decode base64 audio content: {e}")

    # Convert based on encoding type
    if encoding == "LINEAR16":
        # Convert raw PCM data to AudioSegment
        audio_segment = AudioSegment(
            data=audio_bytes,
            sample_width=2,  # 16-bit = 2 bytes
            frame_rate=sample_rate,
            channels=1,  # Assuming mono
        )
    elif encoding == "MP3":
        # If already MP3, just save directly
        with open(output_file, "wb") as f:
            f.write(audio_bytes)
        return output_file
    else:
        raise ValueError(f"Unsupported audio encoding: {encoding}")

    # Export as MP3
    audio_segment.export(output_file, format="mp3")
    return output_file


# Example usage with JSON file (no ffmpeg method)
if __name__ == "__main__":
    # Load JSON from file
    with open("algieba_livemap_audio.json", "r") as f:
        audio_json = json.load(f)

    # Try the alternative method first (no ffmpeg required for WAV)
    try:
        print("Trying alternative method without ffmpeg...")
        output_file = convert_linear16_to_wav(audio_json, "converted_audio.wav")
        print(f"Successfully converted to WAV: {output_file}")
    except Exception as e:
        print(f"Error with WAV conversion: {e}")

    # If you have ffmpeg configured, try MP3 conversion
    try:
        print("Trying MP3 conversion (requires ffmpeg)...")
        output_file = convert_audio_json_to_mp3(audio_json, "converted_audio.mp3")
        print(f"Successfully converted to MP3: {output_file}")
    except Exception as e:
        print(f"Error with MP3 conversion: {e}")


# Simple WAV conversion (no ffmpeg needed)
def convert_linear16_to_wav(json_data, output_file="output.wav"):
    """Convert to WAV without requiring ffmpeg"""
    import wave

    audio_config = json_data.get("audioConfig", {})
    audio_content = json_data.get("audioContent", "")

    sample_rate = audio_config.get("sampleRateHertz", 24000)
    audio_bytes = base64.b64decode(audio_content)

    with wave.open(output_file, "wb") as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_bytes)

    return output_file


# Alternative method using wave module for LINEAR16
def convert_linear16_to_mp3_alternative(json_data, output_file="output.mp3"):
    """Alternative method using wave module"""
    import wave

    audio_config = json_data.get("audioConfig", {})
    audio_content = json_data.get("audioContent", "")

    sample_rate = audio_config.get("sampleRateHertz", 24000)
    audio_bytes = base64.b64decode(audio_content)

    # Create temporary WAV file
    temp_wav = "temp_audio.wav"
    with wave.open(temp_wav, "wb") as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_bytes)

    # Convert WAV to MP3 using pydub
    audio = AudioSegment.from_wav(temp_wav)
    audio.export(output_file, format="mp3")

    # Clean up temp file
    import os

    os.remove(temp_wav)

    return output_file
