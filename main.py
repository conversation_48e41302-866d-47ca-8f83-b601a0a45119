import json
import base64
import numpy as np
from pydub import AudioSegment
from pydub.utils import which
import io

# Configure ffmpeg path (choose one method)
# Method 1: Set ffmpeg path directly
AudioSegment.converter = "/Users/<USER>/ffmpeg"  # Replace with your ffmpeg path
AudioSegment.ffmpeg = "/Users/<USER>/ffmpeg"
# AudioSegment.ffprobe = "/usr/local/bin/ffprobe"

# Method 2: Set via environment (uncomment if needed)
# import os
# os.environ["PATH"] += os.pathsep + "/usr/local/bin"


def convert_audio_json_to_wav(json_data, output_file="output.wav"):
    """
    Convert audio JSON object to WAV file (lossless, highest quality)

    Args:
        json_data: Dictionary containing audio configuration and base64 content
        output_file: Output WAV filename
    """
    import wave

    # Extract audio configuration
    audio_config = json_data.get("audioConfig", {})
    audio_content = json_data.get("audioContent", "")

    # Get audio parameters
    sample_rate = audio_config.get("sampleRateHertz", 24000)
    encoding = audio_config.get("audioEncoding", "LINEAR16")

    # Decode base64 audio content
    try:
        audio_bytes = base64.b64decode(audio_content)
    except Exception as e:
        raise ValueError(f"Failed to decode base64 audio content: {e}")

    if encoding == "LINEAR16":
        # Use wave module for lossless conversion
        with wave.open(output_file, "wb") as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_bytes)
    else:
        raise ValueError(f"Unsupported audio encoding for WAV: {encoding}")

    return output_file


def convert_audio_json_to_mp3(json_data, output_file="output.mp3", quality="high"):
    """
    Convert audio JSON object to MP3 file with quality options

    Args:
        json_data: Dictionary containing audio configuration and base64 content
        output_file: Output MP3 filename
        quality: Quality level - "low" (128kbps), "medium" (192kbps), "high" (320kbps)
    """

    # Extract audio configuration
    audio_config = json_data.get("audioConfig", {})
    audio_content = json_data.get("audioContent", "")

    # Get audio parameters
    sample_rate = audio_config.get("sampleRateHertz", 24000)
    encoding = audio_config.get("audioEncoding", "LINEAR16")

    # Decode base64 audio content
    try:
        audio_bytes = base64.b64decode(audio_content)
    except Exception as e:
        raise ValueError(f"Failed to decode base64 audio content: {e}")

    # Quality settings
    quality_settings = {
        "low": {"bitrate": "128k"},
        "medium": {"bitrate": "192k"},
        "high": {"bitrate": "320k"}
    }

    if quality not in quality_settings:
        quality = "high"  # Default to high quality

    bitrate = quality_settings[quality]["bitrate"]

    # Convert based on encoding type
    if encoding == "LINEAR16":
        # Convert raw PCM data to AudioSegment
        audio_segment = AudioSegment(
            data=audio_bytes,
            sample_width=2,  # 16-bit = 2 bytes
            frame_rate=sample_rate,
            channels=1,  # Assuming mono
        )
    elif encoding == "MP3":
        # If already MP3, just save directly
        with open(output_file, "wb") as f:
            f.write(audio_bytes)
        return output_file
    else:
        raise ValueError(f"Unsupported audio encoding: {encoding}")

    # Export as MP3 with high quality settings
    audio_segment.export(
        output_file,
        format="mp3",
        bitrate=bitrate,
        parameters=["-q:a", "0"]  # Highest quality VBR setting
    )
    return output_file


def convert_audio_json_to_flac(json_data, output_file="output.flac"):
    """
    Convert audio JSON object to FLAC file (lossless compression)

    Args:
        json_data: Dictionary containing audio configuration and base64 content
        output_file: Output FLAC filename
    """

    # Extract audio configuration
    audio_config = json_data.get("audioConfig", {})
    audio_content = json_data.get("audioContent", "")

    # Get audio parameters
    sample_rate = audio_config.get("sampleRateHertz", 24000)
    encoding = audio_config.get("audioEncoding", "LINEAR16")

    # Decode base64 audio content
    try:
        audio_bytes = base64.b64decode(audio_content)
    except Exception as e:
        raise ValueError(f"Failed to decode base64 audio content: {e}")

    if encoding == "LINEAR16":
        # Convert raw PCM data to AudioSegment
        audio_segment = AudioSegment(
            data=audio_bytes,
            sample_width=2,  # 16-bit = 2 bytes
            frame_rate=sample_rate,
            channels=1,  # Assuming mono
        )

        # Export as FLAC (lossless compression)
        audio_segment.export(output_file, format="flac")
    else:
        raise ValueError(f"Unsupported audio encoding for FLAC: {encoding}")

    return output_file


# Example usage with JSON file
if __name__ == "__main__":
    # Load JSON from file
    with open("algieba_livemap_audio.json", "r") as f:
        audio_json = json.load(f)

    print("🎵 Audio Quality Conversion Options:")
    print("=" * 50)

    # 1. WAV conversion (highest quality, lossless)
    try:
        print("1. Converting to WAV (lossless, highest quality)...")
        wav_file = convert_audio_json_to_wav(audio_json, "converted_audio_hq.wav")
        print(f"✅ Successfully converted to WAV: {wav_file}")
    except Exception as e:
        print(f"❌ Error with WAV conversion: {e}")

    # 2. FLAC conversion (lossless compression)
    try:
        print("\n2. Converting to FLAC (lossless compression)...")
        flac_file = convert_audio_json_to_flac(audio_json, "converted_audio_hq.flac")
        print(f"✅ Successfully converted to FLAC: {flac_file}")
    except Exception as e:
        print(f"❌ Error with FLAC conversion: {e}")

    # 3. High-quality MP3 conversion
    try:
        print("\n3. Converting to high-quality MP3 (320kbps)...")
        mp3_hq_file = convert_audio_json_to_mp3(audio_json, "converted_audio_hq.mp3", quality="high")
        print(f"✅ Successfully converted to high-quality MP3: {mp3_hq_file}")
    except Exception as e:
        print(f"❌ Error with high-quality MP3 conversion: {e}")

    # 4. Medium-quality MP3 conversion (for smaller file size)
    try:
        print("\n4. Converting to medium-quality MP3 (192kbps)...")
        mp3_med_file = convert_audio_json_to_mp3(audio_json, "converted_audio_med.mp3", quality="medium")
        print(f"✅ Successfully converted to medium-quality MP3: {mp3_med_file}")
    except Exception as e:
        print(f"❌ Error with medium-quality MP3 conversion: {e}")

    print("\n🎯 Recommendation: Use WAV for highest quality, FLAC for lossless compression, or high-quality MP3 for compatibility.")


def convert_audio_with_best_quality(json_data, output_format="wav", quality="high"):
    """
    Convert audio with the best quality settings for the chosen format

    Args:
        json_data: Dictionary containing audio configuration and base64 content
        output_format: "wav", "flac", or "mp3"
        quality: For MP3 only - "low", "medium", "high"

    Returns:
        str: Output filename
    """

    if output_format.lower() == "wav":
        return convert_audio_json_to_wav(json_data, "output_best_quality.wav")
    elif output_format.lower() == "flac":
        return convert_audio_json_to_flac(json_data, "output_best_quality.flac")
    elif output_format.lower() == "mp3":
        return convert_audio_json_to_mp3(json_data, "output_best_quality.mp3", quality=quality)
    else:
        raise ValueError(f"Unsupported format: {output_format}. Use 'wav', 'flac', or 'mp3'")


def get_audio_info(json_data):
    """
    Get information about the audio data

    Args:
        json_data: Dictionary containing audio configuration and base64 content

    Returns:
        dict: Audio information
    """
    audio_config = json_data.get("audioConfig", {})
    audio_content = json_data.get("audioContent", "")

    sample_rate = audio_config.get("sampleRateHertz", 24000)
    encoding = audio_config.get("audioEncoding", "LINEAR16")

    # Calculate approximate duration and file size
    audio_bytes = base64.b64decode(audio_content)
    bytes_per_sample = 2  # 16-bit
    channels = 1  # Mono

    total_samples = len(audio_bytes) // (bytes_per_sample * channels)
    duration_seconds = total_samples / sample_rate

    return {
        "sample_rate": sample_rate,
        "encoding": encoding,
        "channels": channels,
        "duration_seconds": round(duration_seconds, 2),
        "raw_size_bytes": len(audio_bytes),
        "raw_size_mb": round(len(audio_bytes) / (1024 * 1024), 2)
    }


